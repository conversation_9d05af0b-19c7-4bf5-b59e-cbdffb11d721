const fs = require("fs");
const path = require("path");
const pdf = require("pdf-parse");
const { extractFieldsFromText } = require("./utils/enhancedExtractText");

// Directory containing the PDF files
const invoicesDir = path.resolve(__dirname, "invoices");

// Get all PDF files in the directory
const pdfFiles = fs.readdirSync(invoicesDir).filter(file => file.toLowerCase().endsWith('.pdf'));

// Process each PDF file
async function processAllPdfs() {
    const results = [];
    
    for (const pdfFile of pdfFiles) {
        const filePath = path.join(invoicesDir, pdfFile);
        console.log(`\n\nProcessing: ${pdfFile}`);
        
        try {
            // Extract text from PDF
            const dataBuffer = fs.readFileSync(filePath);
            const data = await pdf(dataBuffer);
            const text = data.text;
            
            // Extract fields and items using enhanced extraction
            const result = extractFieldsFromText(text);
            
            // Add filename to the result
            result.filename = pdfFile;
            
            // Add to results array
            results.push(result);
            
            // Save individual result for debugging
            const outputPath = path.join(__dirname, `output_${pdfFile.replace('.pdf', '.json')}`);
            fs.writeFileSync(outputPath, JSON.stringify(result, null, 2));
            console.log(`✅ Individual output written to ${outputPath}`);
            
        } catch (error) {
            console.error(`Error processing ${pdfFile}:`, error);
            results.push({
                filename: pdfFile,
                error: error.message
            });
        }
    }
    
    // Write combined results to output.json
    fs.writeFileSync("output.json", JSON.stringify(results, null, 2));
    console.log("\n✅ Combined output written to output.json");
}

// Run the process
processAllPdfs().catch(error => {
    console.error("Error:", error);
});