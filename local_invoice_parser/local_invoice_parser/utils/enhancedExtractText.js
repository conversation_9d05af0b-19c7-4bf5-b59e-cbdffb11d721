/**
 * Enhanced text extraction utility for PDF invoices
 * Handles different document types, formats, and text alignments
 */

/**
 * Main function to extract fields and items from PDF text
 * @param {string} text - Raw text extracted from PDF
 * @return {Object} Object containing extracted fields and items
 */
function extractFieldsFromText(text) {
    // Split text into lines and clean them
    const lines = text.split(/\n/).map(l => l.trim()).filter(Boolean);
    
    // Determine document type
    const docType = determineDocumentType(lines);
    
    // Extract basic fields common across document types
    const basicFields = extractBasicFields(lines);
    
    // Extract document-specific fields
    const specificFields = extractSpecificFields(lines, docType);
    
    // Extract items based on document type
    const items = extractItems(lines, docType);
    
    // Combine all fields
    const fields = {
        documentType: docType,
        ...basicFields,
        ...specificFields
    };
    
    return { fields, items };
}

/**
 * Determine the type of document based on text content
 * @param {string[]} lines - Array of text lines
 * @return {string} Document type
 */
function determineDocumentType(lines) {
    const firstFewLines = lines.slice(0, 10).join(' ').toLowerCase();
    
    if (firstFewLines.includes('purchase order')) {
        return 'PURCHASE_ORDER';
    } else if (firstFewLines.includes('delivery challan')) {
        return 'DELIVERY_CHALLAN';
    } else if (firstFewLines.includes('tax invoice')) {
        return 'TAX_INVOICE';
    } else if (firstFewLines.includes('b2c job order')) {
        return 'JOB_ORDER';
    } else {
        return 'UNKNOWN';
    }
}

/**
 * Extract basic fields common across all document types
 * @param {string[]} lines - Array of text lines
 * @return {Object} Extracted basic fields
 */
function extractBasicFields(lines) {
    const fields = {};
    
    // Company information
    const companyInfo = extractCompanyInfo(lines);
    if (companyInfo) {
        fields.companyName = companyInfo.name;
        fields.companyAddress = companyInfo.address;
    }
    
    // Common field patterns
    const commonPatterns = [
        // GSTIN/UIN patterns
        [/GSTIN\/?UIN\s*[:\-]?\s*([0-9A-Z]{15})/i, "GSTIN"],
        [/GSTIN\/UIN\s*[:]\s*([0-9A-Z]{15})/i, "GSTIN"],
        
        // PAN/IT patterns
        [/PAN\/?IT\s*No\s*[:\-]?\s*([A-Z0-9]{10})/i, "PAN"],
        [/PAN\/IT No\s*[:]\s*([A-Z0-9]{10})/i, "PAN"],
        
        // Email patterns
        [/E-Mail\s*[:\-]?\s*([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/i, "Email"],
        
        // State information
        [/State Name\s*[:\-]?\s*([^,]+),\s*Code\s*[:\-]?\s*(\d+)/i, "State"],
        
        // Date patterns
        [/Date\s*[:\-]?\s*(\d{1,2}[-\/][A-Za-z]{3}[-\/]\d{2,4})/i, "Date"],
        [/Date\s*[:\-]?\s*(\d{1,2}[-\/]\d{1,2}[-\/]\d{2,4})/i, "Date"]
    ];
    
    // Extract fields using patterns
    for (let i = 0; i < lines.length; i++) {
        for (const [regex, key] of commonPatterns) {
            const match = lines[i].match(regex);
            if (match && match[1] && !fields[key]) {
                let value = match[1].trim();
                
                // Handle special case for State which has two capture groups
                if (key === "State" && match[2]) {
                    value = `${value}, Code: ${match[2]}`;
                }
                
                // Check if value is a placeholder and look for actual value in next line
                if (value === "." || value === ":" || value.toLowerCase() === "ed") {
                    const nextLine = lines[i + 1]?.trim();
                    if (nextLine && !nextLine.match(/^[A-Z\s]{4,}$/)) {
                        value = nextLine;
                        i++;
                    }
                }
                
                fields[key] = value;
            }
        }
    }
    
    // Extract consignee and buyer information
    const consigneeInfo = extractAddressBlock(lines, "Consignee", "Ship to");
    if (consigneeInfo) {
        fields.consigneeName = consigneeInfo.name;
        fields.consigneeAddress = consigneeInfo.address;
        fields.consigneeGSTIN = consigneeInfo.GSTIN;
    }
    
    const buyerInfo = extractAddressBlock(lines, "Buyer", "Bill to");
    if (buyerInfo) {
        fields.buyerName = buyerInfo.name;
        fields.buyerAddress = buyerInfo.address;
        fields.buyerGSTIN = buyerInfo.GSTIN;
    }
    
    return fields;
}

/**
 * Extract company information from the beginning of the document
 * @param {string[]} lines - Array of text lines
 * @return {Object|null} Company name and address or null if not found
 */
function extractCompanyInfo(lines) {
    // Skip any empty lines at the beginning
    let startIndex = 0;
    while (startIndex < lines.length && !lines[startIndex]) {
        startIndex++;
    }
    
    // Company name is typically the first non-empty line after any document type header
    if (startIndex < lines.length) {
        const firstLine = lines[startIndex];
        
        // Skip document type headers if present
        if (firstLine.match(/^(Tax Invoice|Delivery Challan|Purchase Order|B2C Job Order)/i)) {
            startIndex++;
        }
        
        if (startIndex < lines.length) {
            const companyName = lines[startIndex];
            
            // Address typically follows the company name (next 2-4 lines)
            const addressLines = [];
            let i = startIndex + 1;
            
            // Collect address lines until we hit something that looks like a section header or field
            while (i < lines.length && 
                   i < startIndex + 5 && 
                   !lines[i].match(/^(GSTIN|PAN|E-Mail|Consignee|Buyer|State Name)/i)) {
                addressLines.push(lines[i]);
                i++;
            }
            
            return {
                name: companyName,
                address: addressLines.join(", ")
            };
        }
    }
    
    return null;
}

/**
 * Extract address block information (Consignee or Buyer)
 * @param {string[]} lines - Array of text lines
 * @param {string} blockType - Type of address block ("Consignee" or "Buyer")
 * @param {string} subType - Subtype of address block ("Ship to" or "Bill to")
 * @return {Object|null} Address information or null if not found
 */
function extractAddressBlock(lines, blockType, subType) {
    const blockRegex = new RegExp(`${blockType}\\s*\\(${subType}\\)`, 'i');
    
    // Find the start of the address block
    let startIndex = -1;
    for (let i = 0; i < lines.length; i++) {
        if (blockRegex.test(lines[i])) {
            startIndex = i + 1;
            break;
        }
    }
    
    if (startIndex === -1) return null;
    
    // Extract name (first line after the header)
    const name = lines[startIndex];
    
    // Extract address (lines until we hit GSTIN or another section)
    const addressLines = [];
    let i = startIndex + 1;
    
    while (i < lines.length && 
           !lines[i].match(/^(GSTIN|PAN|Buyer|Consignee|E-Mail)/i)) {
        addressLines.push(lines[i]);
        i++;
    }
    
    // Extract GSTIN if present
    let GSTIN = null;
    if (i < lines.length && lines[i].match(/GSTIN\/UIN\s*[:\-]?\s*/i)) {
        const match = lines[i].match(/GSTIN\/UIN\s*[:\-]?\s*([0-9A-Z]{15})/i);
        if (match && match[1]) {
            GSTIN = match[1];
        }
    }
    
    return {
        name,
        address: addressLines.join(", "),
        GSTIN
    };
}

/**
 * Extract document-specific fields based on document type
 * @param {string[]} lines - Array of text lines
 * @param {string} docType - Document type
 * @return {Object} Document-specific fields
 */
function extractSpecificFields(lines, docType) {
    const fields = {};
    
    switch (docType) {
        case 'TAX_INVOICE':
            // Extract IRN, Ack No, Ack Date for Tax Invoices
            for (let i = 0; i < lines.length; i++) {
                // IRN pattern
                const irnMatch = lines[i].match(/IRN\s*:\s*([a-f0-9-]+)/i);
                if (irnMatch && irnMatch[1] && !fields.IRN) {
                    fields.IRN = irnMatch[1];
                }
                
                // Ack No pattern
                const ackNoMatch = lines[i].match(/Ack No\s*[.:]?\s*(\d+)/i);
                if (ackNoMatch && ackNoMatch[1] && !fields.AckNo) {
                    fields.AckNo = ackNoMatch[1];
                }
                
                // Ack Date pattern
                const ackDateMatch = lines[i].match(/Ack Date\s*[.:]?\s*(\d{1,2}-[A-Za-z]{3}-\d{2,4})/i);
                if (ackDateMatch && ackDateMatch[1] && !fields.AckDate) {
                    fields.AckDate = ackDateMatch[1];
                }
            }
            break;
            
        case 'DELIVERY_CHALLAN':
            // Extract Delivery Note No, Dispatched Through, Destination
            const deliveryChallanPatterns = [
                [/Delivery\s*Note\s*No\.?\s*[:\-]?\s*(.*)/i, "DeliveryNoteNo"],
                [/Dispatched?\s*Through\s*[:\-]?\s*(.*)/i, "DispatchedThrough"],
                [/Destination\s*[:\-]?\s*(.*)/i, "Destination"],
                [/Mode\/Terms of Payment\s*[:\-]?\s*(.*)/i, "PaymentTerms"]
            ];
            
            for (let i = 0; i < lines.length; i++) {
                for (const [regex, key] of deliveryChallanPatterns) {
                    const match = lines[i].match(regex);
                    if (match && match[1] && !fields[key]) {
                        fields[key] = match[1].trim();
                    }
                }
            }
            break;
            
        case 'PURCHASE_ORDER':
            // Extract PO Number, PO Date, Vendor Code
            const poPatterns = [
                [/P\.?O\.?\s*No\.?\s*[:\-#]?\s*([A-Z0-9-]+)/i, "PONumber"],
                [/P\.?O\.?\s*Date\s*[:\-]?\s*(\d{1,2}[-\/][A-Za-z]{3}[-\/]\d{2,4}|\d{1,2}[-\/]\d{1,2}[-\/]\d{2,4})/i, "PODate"],
                [/Vendor\s*Code\s*[:\-]?\s*([A-Z0-9]+)/i, "VendorCode"]
            ];
            
            for (let i = 0; i < lines.length; i++) {
                for (const [regex, key] of poPatterns) {
                    const match = lines[i].match(regex);
                    if (match && match[1] && !fields[key]) {
                        fields[key] = match[1].trim();
                    }
                }
            }
            break;
            
        case 'JOB_ORDER':
            // Extract Job Order specific fields
            const jobOrderPatterns = [
                [/Order\s*No\.?\s*[:\-]?\s*([A-Z0-9-]+)/i, "OrderNo"],
                [/Order\s*Date\s*[:\-]?\s*(\d{1,2}[-\/][A-Za-z]{3}[-\/]\d{2,4}|\d{1,2}[-\/]\d{1,2}[-\/]\d{2,4})/i, "OrderDate"]
            ];
            
            for (let i = 0; i < lines.length; i++) {
                for (const [regex, key] of jobOrderPatterns) {
                    const match = lines[i].match(regex);
                    if (match && match[1] && !fields[key]) {
                        fields[key] = match[1].trim();
                    }
                }
            }
            break;
    }
    
    return fields;
}

/**
 * Extract items from the document based on document type
 * @param {string[]} lines - Array of text lines
 * @param {string} docType - Document type
 * @return {Array} Array of extracted items
 */
function extractItems(lines, docType) {
    const items = [];
    
    // Find the items section
    let itemSectionStart = -1;
    for (let i = 0; i < lines.length; i++) {
        // Look for common item section headers
        if (lines[i].match(/^(S\.?No\.?|Item\s*Code|Description|HSN\/SAC|Qty|Rate|Amount|Item\s*Description)/i) &&
            lines[i+1] && lines[i+1].match(/^[-]+$/)) {
            itemSectionStart = i + 2; // Skip the header and separator line
            break;
        }
    }
    
    if (itemSectionStart === -1) {
        // Try alternative approach - look for patterns in the lines
        for (let i = 0; i < lines.length; i++) {
            // Different item patterns based on document type
            let itemMatch = null;
            
            switch (docType) {
                case 'TAX_INVOICE':
                case 'DELIVERY_CHALLAN':
                case 'JOB_ORDER':
                    // Pattern for Resonate documents
                    itemMatch = lines[i].match(/^(?:\d+\s+)?(RSNT-[A-Z0-9\-]+)\s+(\d{6,8})\s+(\d+(?:\.\d{1,2})?)\s+(NOS|PCS|Units|EA)/i);
                    if (itemMatch) {
                        const [, code, hsn, qty, unit] = itemMatch;
                        
                        // Grab 1–2 lines below for description
                        const descLines = [];
                        for (let j = 1; j <= 2 && i + j < lines.length; j++) {
                            const desc = lines[i + j].trim();
                            if (/^(IGST|CGST|SGST|Total|RSNT-)/i.test(desc)) break;
                            descLines.push(desc);
                        }
                        
                        // Look for price and amount
                        let rate = null;
                        let amount = null;
                        
                        // Check next few lines for rate and amount
                        for (let j = 1; j <= 4 && i + j < lines.length; j++) {
                            const priceLine = lines[i + j];
                            const priceMatch = priceLine.match(/(\d{1,3}(?:,\d{3})*(?:\.\d{2}))/g);
                            if (priceMatch && priceMatch.length >= 2) {
                                rate = priceMatch[0];
                                amount = priceMatch[1];
                                break;
                            }
                        }
                        
                        items.push({
                            code,
                            hsn,
                            qty,
                            unit,
                            description: descLines.join(" "),
                            rate,
                            amount
                        });
                        
                        i += descLines.length; // skip description lines
                    }
                    break;
                    
                case 'PURCHASE_ORDER':
                    // Pattern for PO items
                    itemMatch = lines[i].match(/^(?:\d+\s+)?([A-Z0-9-]+)\s+(.+?)\s+(\d+(?:\.\d{1,2})?)\s+(NOS|PCS|Units|EA)/i);
                    if (itemMatch) {
                        const [, itemCode, description, qty, unit] = itemMatch;
                        
                        // Look for price and amount
                        let unitPrice = null;
                        let amount = null;
                        
                        // Check next few lines for price and amount
                        for (let j = 1; j <= 3 && i + j < lines.length; j++) {
                            const priceLine = lines[i + j];
                            const priceMatch = priceLine.match(/(\d{1,3}(?:,\d{3})*(?:\.\d{2}))/g);
                            if (priceMatch && priceMatch.length >= 2) {
                                unitPrice = priceMatch[0];
                                amount = priceMatch[1];
                                break;
                            }
                        }
                        
                        items.push({
                            itemCode,
                            description,
                            qty,
                            unit,
                            unitPrice,
                            amount
                        });
                    }
                    break;
            }
        }
    } else {
        // Process items from the identified section
        for (let i = itemSectionStart; i < lines.length; i++) {
            const line = lines[i].trim();
            
            // Stop when we reach totals or end of items section
            if (line.match(/^(Total|Sub\s*Total|Grand\s*Total)/i)) {
                break;
            }
            
            // Skip empty lines
            if (!line) continue;
            
            // Different parsing logic based on document type
            switch (docType) {
                case 'TAX_INVOICE':
                case 'DELIVERY_CHALLAN':
                case 'JOB_ORDER':
                    // Try to parse item line
                    const itemMatch = line.match(/^(?:\d+\s+)?([A-Z0-9-]+)\s+(\d{6,8})\s+(\d+(?:\.\d{1,2})?)\s+(NOS|PCS|Units|EA)/i);
                    if (itemMatch) {
                        const [, code, hsn, qty, unit] = itemMatch;
                        
                        // Look for description, rate, and amount in subsequent lines
                        let description = "";
                        let rate = null;
                        let amount = null;
                        
                        // Check next few lines
                        for (let j = 1; j <= 3 && i + j < lines.length; j++) {
                            const nextLine = lines[i + j].trim();
                            
                            // Stop if we hit another item or totals
                            if (nextLine.match(/^(?:\d+\s+)?[A-Z0-9-]+\s+\d{6,8}/i) || 
                                nextLine.match(/^(Total|Sub\s*Total|Grand\s*Total)/i)) {
                                break;
                            }
                            
                            // Check for price information
                            const priceMatch = nextLine.match(/(\d{1,3}(?:,\d{3})*(?:\.\d{2}))/g);
                            if (priceMatch && priceMatch.length >= 2) {
                                rate = priceMatch[0];
                                amount = priceMatch[1];
                            } else if (!nextLine.match(/^[0-9.,%]+$/)) {
                                // If not a price line and not just numbers, treat as description
                                description += (description ? " " : "") + nextLine;
                            }
                        }
                        
                        items.push({
                            code,
                            hsn,
                            qty,
                            unit,
                            description,
                            rate,
                            amount
                        });
                    }
                    break;
                    
                case 'PURCHASE_ORDER':
                    // PO item format is different
                    const poItemMatch = line.match(/^(?:\d+\s+)?([A-Z0-9-]+)\s+(.+?)\s+(\d+(?:\.\d{1,2})?)\s+(NOS|PCS|Units|EA)/i);
                    if (poItemMatch) {
                        const [, itemCode, description, qty, unit] = poItemMatch;
                        
                        // Look for price information in subsequent lines
                        let unitPrice = null;
                        let amount = null;
                        
                        // Check next few lines
                        for (let j = 1; j <= 2 && i + j < lines.length; j++) {
                            const nextLine = lines[i + j].trim();
                            
                            // Check for price information
                            const priceMatch = nextLine.match(/(\d{1,3}(?:,\d{3})*(?:\.\d{2}))/g);
                            if (priceMatch && priceMatch.length >= 2) {
                                unitPrice = priceMatch[0];
                                amount = priceMatch[1];
                                break;
                            }
                        }
                        
                        items.push({
                            itemCode,
                            description,
                            qty,
                            unit,
                            unitPrice,
                            amount
                        });
                    }
                    break;
            }
        }
    }
    
    return items;
}

module.exports = { extractFieldsFromText };