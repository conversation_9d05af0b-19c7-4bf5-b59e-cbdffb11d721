const fs = require("fs");
const path = require("path");
const pdf = require("pdf-parse");
const { extractFieldsFromText } = require("./utils/enhancedExtractText");

// Directory containing the PDF files
const invoicesDir = path.resolve(__dirname, "invoices");

// Get all PDF files in the directory
const pdfFiles = fs.readdirSync(invoicesDir).filter(file => file.toLowerCase().endsWith('.pdf'));

// Process each PDF file
async function processAllPdfs() {
    const results = [];
    let successCount = 0;
    let errorCount = 0;

    console.log(`\n🚀 Starting to process ${pdfFiles.length} PDF files...\n`);

    for (const pdfFile of pdfFiles) {
        const filePath = path.join(invoicesDir, pdfFile);
        console.log(`📄 Processing: ${pdfFile}`);

        try {
            // Extract text from PDF
            const dataBuffer = fs.readFileSync(filePath);
            const data = await pdf(dataBuffer);
            const text = data.text;

            // Extract fields and items using enhanced extraction
            const result = extractFieldsFromText(text);

            // Add filename and processing metadata
            result.filename = pdfFile;
            result.processedAt = new Date().toISOString();
            result.extractionSummary = {
                documentType: result.fields.documentType,
                fieldsExtracted: Object.keys(result.fields).length,
                itemsExtracted: result.items.length,
                hasCompanyInfo: !!(result.fields.companyName && result.fields.companyAddress),
                hasConsigneeInfo: !!(result.fields.consigneeName && result.fields.consigneeAddress),
                hasBuyerInfo: !!(result.fields.buyerName && result.fields.buyerAddress)
            };

            // Add to results array
            results.push(result);
            successCount++;

            // Save individual result for debugging
            const outputPath = path.join(__dirname, `output_${pdfFile.replace('.pdf', '.json')}`);
            fs.writeFileSync(outputPath, JSON.stringify(result, null, 2));

            console.log(`   ✅ Success: ${result.extractionSummary.fieldsExtracted} fields, ${result.extractionSummary.itemsExtracted} items`);
            console.log(`   📁 Individual output: output_${pdfFile.replace('.pdf', '.json')}`);

        } catch (error) {
            console.error(`   ❌ Error processing ${pdfFile}:`, error.message);
            errorCount++;
            results.push({
                filename: pdfFile,
                error: error.message,
                processedAt: new Date().toISOString(),
                extractionSummary: {
                    documentType: "ERROR",
                    fieldsExtracted: 0,
                    itemsExtracted: 0,
                    hasCompanyInfo: false,
                    hasConsigneeInfo: false,
                    hasBuyerInfo: false
                }
            });
        }
    }

    // Write combined results to output.json
    const finalOutput = {
        processingMetadata: {
            totalFiles: pdfFiles.length,
            successfullyProcessed: successCount,
            errors: errorCount,
            processedAt: new Date().toISOString()
        },
        results: results
    };

    fs.writeFileSync("output.json", JSON.stringify(finalOutput, null, 2));

    console.log(`\n📊 Processing Summary:`);
    console.log(`   Total files: ${pdfFiles.length}`);
    console.log(`   Successfully processed: ${successCount}`);
    console.log(`   Errors: ${errorCount}`);
    console.log(`\n✅ Combined output written to output.json`);
}

// Run the process
processAllPdfs().catch(error => {
    console.error("Error:", error);
});