/**
 * Enhanced text extraction utility for PDF invoices
 * Handles different document types, formats, and text alignments with intelligent field mapping
 */

/**
 * Main function to extract fields and items from PDF text
 * @param {string} text - Raw text extracted from PDF
 * @return {Object} Object containing extracted fields and items
 */
function extractFieldsFromText(text) {
    // Split text into lines and clean them
    const lines = text.split(/\n/).map(l => l.trim()).filter(Boolean);

    // Determine document type
    const docType = determineDocumentType(lines);

    // Extract basic fields common across document types
    const basicFields = extractBasicFields(lines);

    // Extract document-specific fields
    const specificFields = extractSpecificFields(lines, docType);

    // Extract items based on document type
    const items = extractItems(lines, docType);

    // Extract financial information
    const financialInfo = extractFinancialInfo(lines, docType);

    // Combine all fields
    const fields = {
        documentType: docType,
        ...basicFields,
        ...specificFields,
        ...financialInfo
    };

    return { fields, items };
}

/**
 * Determine the type of document based on text content with improved detection
 * @param {string[]} lines - Array of text lines
 * @return {string} Document type
 */
function determineDocumentType(lines) {
    const firstTenLines = lines.slice(0, 15).join(' ').toLowerCase();

    // More specific patterns for better detection
    if (firstTenLines.includes('purchase order') || firstTenLines.includes('po no')) {
        return 'PURCHASE_ORDER';
    } else if (firstTenLines.includes('delivery challan')) {
        return 'DELIVERY_CHALLAN';
    } else if (firstTenLines.includes('tax invoice') || firstTenLines.includes('e-invoice')) {
        return 'TAX_INVOICE';
    } else if (firstTenLines.includes('b2c job order') || firstTenLines.includes('job order')) {
        return 'JOB_ORDER';
    } else {
        // Try to detect based on specific patterns
        for (let i = 0; i < Math.min(lines.length, 15); i++) {
            const line = lines[i].toLowerCase();
            if (line.includes('invoice no') || line.includes('irn:')) {
                return 'TAX_INVOICE';
            } else if (line.includes('delivery note no')) {
                return 'DELIVERY_CHALLAN';
            } else if (line.includes('po no') || line.includes('purchase order')) {
                return 'PURCHASE_ORDER';
            }
        }
        return 'UNKNOWN';
    }
}

/**
 * Extract basic fields common across all document types
 * @param {string[]} lines - Array of text lines
 * @return {Object} Extracted basic fields
 */
function extractBasicFields(lines) {
    const fields = {};
    
    // Company information
    const companyInfo = extractCompanyInfo(lines);
    if (companyInfo) {
        fields.companyName = companyInfo.name;
        fields.companyAddress = companyInfo.address;
    }
    
    // Enhanced common field patterns with better matching
    const commonPatterns = [
        // GSTIN/UIN patterns - more comprehensive
        [/GSTIN\/?UIN\s*[:\-]?\s*([0-9A-Z]{15})/i, "GSTIN"],
        [/GSTIN\/UIN\s*[:]\s*([0-9A-Z]{15})/i, "GSTIN"],
        [/GST\s*No\.?\s*[:\-]?\s*([0-9A-Z]{15})/i, "GSTIN"],
        [/GST\s*Number\s*[:\-]?\s*([0-9A-Z]{15})/i, "GSTIN"],

        // PAN/IT patterns - more comprehensive
        [/PAN\/?IT\s*No\.?\s*[:\-]?\s*([A-Z0-9]{10})/i, "PAN"],
        [/PAN\/IT\s*No\s*[:]\s*([A-Z0-9]{10})/i, "PAN"],
        [/PAN\s*Number\s*[:\-]?\s*([A-Z0-9]{10})/i, "PAN"],
        [/Company's\s*PAN\s*[:\-]?\s*([A-Z0-9]{10})/i, "CompanyPAN"],

        // Email patterns
        [/E-Mail\s*[:\-]?\s*([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/i, "Email"],

        // State information
        [/State Name\s*[:\-]?\s*([^,]+),\s*Code\s*[:\-]?\s*(\d+)/i, "State"],

        // Date patterns - more comprehensive
        [/Date\s*[:\-]?\s*(\d{1,2}[-\/][A-Za-z]{3}[-\/]\d{2,4})/i, "Date"],
        [/Date\s*[:\-]?\s*(\d{1,2}[-\/]\d{1,2}[-\/]\d{2,4})/i, "Date"],
        [/Dated\s*[:\-]?\s*(\d{1,2}[-\/][A-Za-z]{3}[-\/]\d{2,4})/i, "Date"],
        [/Dated\s*[:\-]?\s*(\d{1,2}[-\/]\d{1,2}[-\/]\d{2,4})/i, "Date"]
    ];
    
    // Extract fields using patterns with improved logic
    for (let i = 0; i < lines.length; i++) {
        for (const [regex, key] of commonPatterns) {
            const match = lines[i].match(regex);
            if (match && match[1] && !fields[key]) {
                let value = match[1].trim();

                // Handle special case for State which has two capture groups
                if (key === "State" && match[2]) {
                    value = `${value}, Code: ${match[2]}`;
                }

                // Check if value is a placeholder and look for actual value in next line
                if (value === "." || value === ":" || value.toLowerCase() === "ed" || value === "") {
                    const nextLine = lines[i + 1]?.trim();
                    if (nextLine && !nextLine.match(/^[A-Z\s]{4,}$/) &&
                        !nextLine.match(/^(GSTIN|PAN|E-Mail|Consignee|Buyer)/i)) {
                        value = nextLine;
                        i++;
                    }
                }

                // Skip if value is still empty or just punctuation
                if (value && value.length > 1 && !value.match(/^[:\-\.]+$/)) {
                    fields[key] = value;
                }
            }
        }
    }
    
    // Extract consignee and buyer information
    const consigneeInfo = extractAddressBlock(lines, "Consignee", "Ship to");
    if (consigneeInfo) {
        fields.consigneeName = consigneeInfo.name;
        fields.consigneeAddress = consigneeInfo.address;
        fields.consigneeGSTIN = consigneeInfo.GSTIN;
    }
    
    const buyerInfo = extractAddressBlock(lines, "Buyer", "Bill to");
    if (buyerInfo) {
        fields.buyerName = buyerInfo.name;
        fields.buyerAddress = buyerInfo.address;
        fields.buyerGSTIN = buyerInfo.GSTIN;
    }
    
    return fields;
}

/**
 * Extract company information from the beginning of the document with improved logic
 * @param {string[]} lines - Array of text lines
 * @return {Object|null} Company name and address or null if not found
 */
function extractCompanyInfo(lines) {
    // Skip any empty lines at the beginning
    let startIndex = 0;
    while (startIndex < lines.length && !lines[startIndex]) {
        startIndex++;
    }

    // Company name is typically the first non-empty line after any document type header
    if (startIndex < lines.length) {
        let companyNameIndex = startIndex;

        // Skip document type headers, page numbers, and other non-company info
        while (companyNameIndex < lines.length) {
            const line = lines[companyNameIndex];

            // Skip these types of lines
            if (line.match(/^(Tax Invoice|Delivery Challan|Purchase Order|B2C Job Order|Page \d+ of \d+|IRN:|Ack No|Ack Date|e-Invoice)/i) ||
                line.match(/^[0-9a-f-]{30,}$/i) || // Skip long hex strings (IRN)
                line.match(/^\d{10,}$/)) { // Skip long numbers (Ack numbers)
                companyNameIndex++;
                continue;
            }

            // Found potential company name
            break;
        }

        if (companyNameIndex < lines.length) {
            const companyName = lines[companyNameIndex];

            // Skip if this looks like a field rather than company name
            if (companyName.match(/^(GSTIN|PAN|E-Mail|State Name|Consignee|Buyer)/i)) {
                return null;
            }

            // Address typically follows the company name (next 2-5 lines)
            const addressLines = [];
            let i = companyNameIndex + 1;

            // Collect address lines until we hit something that looks like a section header or field
            while (i < lines.length &&
                   i < companyNameIndex + 6 &&
                   !lines[i].match(/^(GSTIN|PAN|E-Mail|Consignee|Buyer|State Name|Invoice No|Delivery Note|PO No)/i)) {

                // Skip lines that look like continuation of headers
                if (!lines[i].match(/^(IRN:|Ack No|Ack Date|e-Invoice)/i) &&
                    !lines[i].match(/^[0-9a-f-]{30,}$/i) &&
                    !lines[i].match(/^\d{10,}$/)) {
                    addressLines.push(lines[i]);
                }
                i++;
            }

            return {
                name: companyName,
                address: addressLines.join(", ")
            };
        }
    }

    return null;
}

/**
 * Extract address block information (Consignee or Buyer)
 * @param {string[]} lines - Array of text lines
 * @param {string} blockType - Type of address block ("Consignee" or "Buyer")
 * @param {string} subType - Subtype of address block ("Ship to" or "Bill to")
 * @return {Object|null} Address information or null if not found
 */
function extractAddressBlock(lines, blockType, subType) {
    const blockRegex = new RegExp(`${blockType}\\s*\\(${subType}\\)`, 'i');
    
    // Find the start of the address block
    let startIndex = -1;
    for (let i = 0; i < lines.length; i++) {
        if (blockRegex.test(lines[i])) {
            startIndex = i + 1;
            break;
        }
    }
    
    if (startIndex === -1) return null;
    
    // Extract name (first line after the header)
    const name = lines[startIndex];
    
    // Extract address (lines until we hit GSTIN or another section)
    const addressLines = [];
    let i = startIndex + 1;
    
    while (i < lines.length && 
           !lines[i].match(/^(GSTIN|PAN|Buyer|Consignee|E-Mail)/i)) {
        addressLines.push(lines[i]);
        i++;
    }
    
    // Extract GSTIN if present
    let GSTIN = null;
    if (i < lines.length && lines[i].match(/GSTIN\/UIN\s*[:\-]?\s*/i)) {
        const match = lines[i].match(/GSTIN\/UIN\s*[:\-]?\s*([0-9A-Z]{15})/i);
        if (match && match[1]) {
            GSTIN = match[1];
        }
    }
    
    return {
        name,
        address: addressLines.join(", "),
        GSTIN
    };
}

/**
 * Extract document-specific fields based on document type
 * @param {string[]} lines - Array of text lines
 * @param {string} docType - Document type
 * @return {Object} Document-specific fields
 */
function extractSpecificFields(lines, docType) {
    const fields = {};
    
    switch (docType) {
        case 'TAX_INVOICE':
            // Extract IRN, Ack No, Ack Date, Invoice No for Tax Invoices
            const taxInvoicePatterns = [
                [/IRN\s*:\s*([a-f0-9-]+)/i, "IRN"],
                [/Ack\s*No\.?\s*[.:]?\s*(\d+)/i, "AckNo"],
                [/Ack\s*Date\s*[.:]?\s*(\d{1,2}-[A-Za-z]{3}-\d{2,4})/i, "AckDate"],
                [/Invoice\s*No\.?\s*[:\-]?\s*([A-Z0-9-]+)/i, "InvoiceNo"],
                [/Delivery\s*Note\s*[:\-]?\s*([A-Z0-9-]+)/i, "DeliveryNote"],
                [/Reference\s*No\.?\s*&\s*Date\.?\s*[:\-]?\s*([^d]+)\s*dt\.\s*(\d{1,2}-[A-Za-z]{3}-\d{2,4})/i, "ReferenceNo"],
                [/Buyer's\s*Order\s*No\.?\s*[:\-]?\s*([A-Z0-9-]+)/i, "BuyerOrderNo"],
                [/Dispatch\s*Doc\s*No\.?\s*[:\-]?\s*([A-Z0-9-]+)/i, "DispatchDocNo"],
                [/Dispatched?\s*through\s*[:\-]?\s*([^D]+)/i, "DispatchedThrough"],
                [/Mode\/Terms\s*of\s*Payment\s*[:\-]?\s*([^O]+)/i, "PaymentTerms"],
                [/Delivery\s*Note\s*Date\s*[:\-]?\s*(\d{1,2}-[A-Za-z]{3}-\d{2,4})/i, "DeliveryNoteDate"]
            ];

            for (let i = 0; i < lines.length; i++) {
                for (const [regex, key] of taxInvoicePatterns) {
                    const match = lines[i].match(regex);
                    if (match && match[1] && !fields[key]) {
                        let value = match[1].trim();

                        // Handle reference number with date
                        if (key === "ReferenceNo" && match[2]) {
                            fields[key] = value;
                            fields["ReferenceDate"] = match[2];
                        } else {
                            fields[key] = value;
                        }
                    }
                }
            }
            break;
            
        case 'DELIVERY_CHALLAN':
            // Extract Delivery Note No, Dispatched Through, Destination
            const deliveryChallanPatterns = [
                [/Delivery\s*Note\s*No\.?\s*[:\-]?\s*([A-Z0-9-]+)/i, "DeliveryNoteNo"],
                [/Reference\s*No\.?\s*&\s*Date\.?\s*[:\-]?\s*([^B]+)/i, "ReferenceNo"],
                [/Buyer's\s*Order\s*No\.?\s*[:\-]?\s*([A-Z0-9-]+)/i, "BuyerOrderNo"],
                [/Dispatch\s*Doc\s*No\.?\s*[:\-]?\s*([A-Z0-9-]+)/i, "DispatchDocNo"],
                [/Dispatched?\s*through\s*[:\-]?\s*([^D]+)/i, "DispatchedThrough"],
                [/Destination\s*[:\-]?\s*([^T]+)/i, "Destination"],
                [/Mode\/Terms\s*of\s*Payment\s*[:\-]?\s*([^O]+)/i, "PaymentTerms"],
                [/Other\s*References\s*[:\-]?\s*([^D]+)/i, "OtherReferences"],
                [/Terms\s*of\s*Delivery\s*[:\-]?\s*(.*)/i, "DeliveryTerms"]
            ];

            for (let i = 0; i < lines.length; i++) {
                for (const [regex, key] of deliveryChallanPatterns) {
                    const match = lines[i].match(regex);
                    if (match && match[1] && !fields[key]) {
                        let value = match[1].trim();
                        // Clean up trailing text that might be part of next field
                        value = value.replace(/\s*(Dated|Mode|Terms|Other).*$/i, '');
                        if (value && value.length > 0) {
                            fields[key] = value;
                        }
                    }
                }
            }
            break;
            
        case 'PURCHASE_ORDER':
            // Extract PO Number, PO Date, Vendor Code, Partner Code
            const poPatterns = [
                [/P\.?O\.?\s*No\.?\s*[:\-#]?\s*([A-Z0-9\-\/]+)/i, "PONumber"],
                [/P\.?O\.?\s*Date\s*[:\-]?\s*(\d{1,2}[-\/][A-Za-z]{3}[-\/]\d{2,4}|\d{1,2}[-\/]\d{1,2}[-\/]\d{2,4})/i, "PODate"],
                [/P\.?O\.?\s*Type\s*[:\-]?\s*([A-Z]+)/i, "POType"],
                [/Rev\s*No\.?\s*[:\-]?\s*(\d+)/i, "RevisionNo"],
                [/Rev\s*Date\s*[:\-]?\s*(\d{1,2}[-\/][A-Za-z]{3}[-\/]\d{2,4}|\d{1,2}[-\/]\d{1,2}[-\/]\d{2,4})/i, "RevisionDate"],
                [/Vendor\s*Code\s*[:\-]?\s*([A-Z0-9]+)/i, "VendorCode"],
                [/Partner\s*Code\s*[:\-]?\s*(\d+)/i, "PartnerCode"],
                [/Currency\s*[:\-]?\s*([A-Za-z\s]+)/i, "Currency"],
                [/Effective\s*From\s*Date\s*[:\-]?\s*(\d{1,2}[-\/][A-Za-z]{3}[-\/]\d{2,4}|\d{1,2}[-\/]\d{1,2}[-\/]\d{2,4})/i, "EffectiveFromDate"],
                [/Effective\s*To\s*Date\s*[:\-]?\s*(\d{1,2}[-\/][A-Za-z]{3}[-\/]\d{2,4}|\d{1,2}[-\/]\d{1,2}[-\/]\d{2,4})/i, "EffectiveToDate"]
            ];

            for (let i = 0; i < lines.length; i++) {
                for (const [regex, key] of poPatterns) {
                    const match = lines[i].match(regex);
                    if (match && match[1] && !fields[key]) {
                        fields[key] = match[1].trim();
                    }
                }
            }
            break;
            
        case 'JOB_ORDER':
            // Extract Job Order specific fields
            const jobOrderPatterns = [
                [/Delivery\s*Note\s*No\.?\s*[:\-]?\s*([A-Z0-9-]+)/i, "DeliveryNoteNo"],
                [/Reference\s*No\.?\s*&\s*Date\.?\s*[:\-]?\s*([^B]+)/i, "ReferenceNo"],
                [/Buyer's\s*Order\s*No\.?\s*[:\-]?\s*([A-Z0-9-]+)/i, "BuyerOrderNo"],
                [/Dispatch\s*Doc\s*No\.?\s*[:\-]?\s*([A-Z0-9-]+)/i, "DispatchDocNo"],
                [/Dispatched?\s*through\s*[:\-]?\s*([^D]+)/i, "DispatchedThrough"],
                [/Destination\s*[:\-]?\s*([^T]+)/i, "Destination"],
                [/Mode\/Terms\s*of\s*Payment\s*[:\-]?\s*([^O]+)/i, "PaymentTerms"],
                [/Other\s*References\s*[:\-]?\s*([^D]+)/i, "OtherReferences"],
                [/Terms\s*of\s*Delivery\s*[:\-]?\s*(.*)/i, "DeliveryTerms"]
            ];

            for (let i = 0; i < lines.length; i++) {
                for (const [regex, key] of jobOrderPatterns) {
                    const match = lines[i].match(regex);
                    if (match && match[1] && !fields[key]) {
                        let value = match[1].trim();
                        // Clean up trailing text that might be part of next field
                        value = value.replace(/\s*(Dated|Mode|Terms|Other).*$/i, '');
                        if (value && value.length > 0) {
                            fields[key] = value;
                        }
                    }
                }
            }
            break;
    }
    
    return fields;
}

/**
 * Extract financial information like totals, taxes, etc.
 * @param {string[]} lines - Array of text lines
 * @param {string} docType - Document type
 * @return {Object} Financial information
 */
function extractFinancialInfo(lines, docType) {
    const financialInfo = {};

    // Look for financial information patterns
    const financialPatterns = [
        [/Total\s*[:\-]?\s*₹?\s*(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)/i, "Total"],
        [/Sub\s*Total\s*[:\-]?\s*₹?\s*(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)/i, "SubTotal"],
        [/Grand\s*Total\s*[:\-]?\s*₹?\s*(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)/i, "GrandTotal"],
        [/IGST\s*@?\s*\d+%?\s*[:\-]?\s*₹?\s*(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)/i, "IGST"],
        [/CGST\s*@?\s*\d+%?\s*[:\-]?\s*₹?\s*(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)/i, "CGST"],
        [/SGST\s*@?\s*\d+%?\s*[:\-]?\s*₹?\s*(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)/i, "SGST"]
    ];

    for (let i = 0; i < lines.length; i++) {
        for (const [regex, key] of financialPatterns) {
            const match = lines[i].match(regex);
            if (match && match[1] && !financialInfo[key]) {
                financialInfo[key] = match[1];
            }
        }
    }

    return financialInfo;
}

/**
 * Extract items from the document based on document type with improved parsing
 * @param {string[]} lines - Array of text lines
 * @param {string} docType - Document type
 * @return {Array} Array of extracted items
 */
function extractItems(lines, docType) {
    const items = [];

    // Different extraction strategies based on document type
    switch (docType) {
        case 'TAX_INVOICE':
        case 'DELIVERY_CHALLAN':
        case 'JOB_ORDER':
            // Extract Resonate items with improved pattern matching
            for (let i = 0; i < lines.length; i++) {
                const line = lines[i];

                // Look for item patterns - handle different formats
                // Pattern 1: "1 RSNT-RUPS-CRU12V2A-BRP7.00 NOS 85044090"
                let itemMatch = line.match(/^(\d+)\s+(RSNT-[A-Z0-9\-]+)(\d+\.\d{2})\s+(NOS|PCS|Units|EA)\s*(\d{6,8})?/i);

                if (!itemMatch) {
                    // Pattern 2: "RSNT-RUPS-CRU12V2A-BRP 7.00 NOS 85044090"
                    itemMatch = line.match(/^(?:\d+\s+)?(RSNT-[A-Z0-9\-]+)\s*(\d+\.\d{2})\s+(NOS|PCS|Units|EA)\s*(\d{6,8})?/i);
                }

                if (!itemMatch) {
                    // Pattern 3: "RSNT-RUPS-CRU12V2A-BRP" on one line, quantity and HSN on next
                    itemMatch = line.match(/^(?:\d+\s+)?(RSNT-[A-Z0-9\-]+)$/i);
                    if (itemMatch && i + 1 < lines.length) {
                        const nextLine = lines[i + 1];
                        const qtyMatch = nextLine.match(/^(\d+\.\d{2})\s+(NOS|PCS|Units|EA)\s*(\d{6,8})?/i);
                        if (qtyMatch) {
                            itemMatch = [line, itemMatch[1], qtyMatch[1], qtyMatch[2], qtyMatch[3]];
                            i++; // Skip the next line as we've processed it
                        }
                    }
                }

                if (itemMatch) {
                    const [, code, qty, unit, hsn] = itemMatch;

                    // Look for description in surrounding lines
                    let description = "";

                    // Check if there's a description after the HSN code
                    if (i + 1 < lines.length) {
                        const nextLine = lines[i + 1].trim();
                        if (nextLine && !nextLine.match(/^(\d+|RSNT-|IGST|CGST|SGST|Total)/i) &&
                            !nextLine.match(/^\d+\.\d{2}\s+(NOS|PCS|Units|EA)/i)) {
                            description = nextLine;
                        }
                    }

                    // Look for price and amount information
                    let rate = null;
                    let amount = null;

                    // Check next few lines for financial information
                    for (let j = 1; j <= 3 && i + j < lines.length; j++) {
                        const priceLine = lines[i + j];
                        const priceMatch = priceLine.match(/(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)/g);
                        if (priceMatch && priceMatch.length >= 2) {
                            rate = priceMatch[0];
                            amount = priceMatch[1];
                            break;
                        }
                    }

                    items.push({
                        serialNo: itemMatch[0] && itemMatch[0].match(/^\d+/) ? itemMatch[0].match(/^\d+/)[0] : items.length + 1,
                        code: code,
                        description: description || "",
                        qty: qty,
                        unit: unit,
                        hsn: hsn || "",
                        rate: rate,
                        amount: amount
                    });
                }
            }
            break;
                    
        case 'PURCHASE_ORDER':
            // Extract Purchase Order items with improved parsing
            let inItemsSection = false;

            for (let i = 0; i < lines.length; i++) {
                const line = lines[i];

                // Detect start of items section
                if (line.match(/^(Item\s*Code|Description|S\.?\s*No\.?|Sl\.?\s*No\.?)/i) ||
                    line.match(/^[-\s]+$/)) {
                    inItemsSection = true;
                    continue;
                }

                // Stop at totals or end sections
                if (line.match(/^(Total|Sub\s*Total|Grand\s*Total|Terms|Conditions)/i)) {
                    break;
                }

                if (inItemsSection) {
                    // Look for item patterns in PO
                    // Pattern: "001 RSNT-RUPS-CRU12V2A-BRP 10 EA"
                    let itemMatch = line.match(/^(\d{3})\s+([A-Z0-9\-]+)\s+(\d+)\s+(EA|NOS|PCS|Units)/i);

                    if (!itemMatch) {
                        // Alternative pattern: "001 Description text 10 EA"
                        itemMatch = line.match(/^(\d{3})\s+(.+?)\s+(\d+)\s+(EA|NOS|PCS|Units)/i);
                    }

                    if (itemMatch) {
                        const [, serialNo, itemCodeOrDesc, qty, unit] = itemMatch;

                        // Determine if second capture is item code or description
                        let itemCode = "";
                        let description = "";

                        if (itemCodeOrDesc.startsWith('RSNT-')) {
                            itemCode = itemCodeOrDesc;
                        } else {
                            description = itemCodeOrDesc;
                        }

                        // Look for price information in next few lines
                        let unitPrice = null;
                        let amount = null;

                        for (let j = 1; j <= 3 && i + j < lines.length; j++) {
                            const priceLine = lines[i + j];
                            const priceMatch = priceLine.match(/(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)/g);
                            if (priceMatch && priceMatch.length >= 2) {
                                unitPrice = priceMatch[0];
                                amount = priceMatch[1];
                                break;
                            }
                        }

                        items.push({
                            serialNo: serialNo,
                            itemCode: itemCode,
                            description: description,
                            qty: qty,
                            unit: unit,
                            unitPrice: unitPrice,
                            amount: amount
                        });
                    }
                }
            }
            break;
    }
    } else {
        // Process items from the identified section
        for (let i = itemSectionStart; i < lines.length; i++) {
            const line = lines[i].trim();
            
            // Stop when we reach totals or end of items section
            if (line.match(/^(Total|Sub\s*Total|Grand\s*Total)/i)) {
                break;
            }
            
            // Skip empty lines
            if (!line) continue;
            
            // Different parsing logic based on document type
            switch (docType) {
                case 'TAX_INVOICE':
                case 'DELIVERY_CHALLAN':
                case 'JOB_ORDER':
                    // Try to parse item line
                    const itemMatch = line.match(/^(?:\d+\s+)?([A-Z0-9-]+)\s+(\d{6,8})\s+(\d+(?:\.\d{1,2})?)\s+(NOS|PCS|Units|EA)/i);
                    if (itemMatch) {
                        const [, code, hsn, qty, unit] = itemMatch;
                        
                        // Look for description, rate, and amount in subsequent lines
                        let description = "";
                        let rate = null;
                        let amount = null;
                        
                        // Check next few lines
                        for (let j = 1; j <= 3 && i + j < lines.length; j++) {
                            const nextLine = lines[i + j].trim();
                            
                            // Stop if we hit another item or totals
                            if (nextLine.match(/^(?:\d+\s+)?[A-Z0-9-]+\s+\d{6,8}/i) || 
                                nextLine.match(/^(Total|Sub\s*Total|Grand\s*Total)/i)) {
                                break;
                            }
                            
                            // Check for price information
                            const priceMatch = nextLine.match(/(\d{1,3}(?:,\d{3})*(?:\.\d{2}))/g);
                            if (priceMatch && priceMatch.length >= 2) {
                                rate = priceMatch[0];
                                amount = priceMatch[1];
                            } else if (!nextLine.match(/^[0-9.,%]+$/)) {
                                // If not a price line and not just numbers, treat as description
                                description += (description ? " " : "") + nextLine;
                            }
                        }
                        
                        items.push({
                            code,
                            hsn,
                            qty,
                            unit,
                            description,
                            rate,
                            amount
                        });
                    }
                    break;
                    
                case 'PURCHASE_ORDER':
                    // PO item format is different
                    const poItemMatch = line.match(/^(?:\d+\s+)?([A-Z0-9-]+)\s+(.+?)\s+(\d+(?:\.\d{1,2})?)\s+(NOS|PCS|Units|EA)/i);
                    if (poItemMatch) {
                        const [, itemCode, description, qty, unit] = poItemMatch;
                        
                        // Look for price information in subsequent lines
                        let unitPrice = null;
                        let amount = null;
                        
                        // Check next few lines
                        for (let j = 1; j <= 2 && i + j < lines.length; j++) {
                            const nextLine = lines[i + j].trim();
                            
                            // Check for price information
                            const priceMatch = nextLine.match(/(\d{1,3}(?:,\d{3})*(?:\.\d{2}))/g);
                            if (priceMatch && priceMatch.length >= 2) {
                                unitPrice = priceMatch[0];
                                amount = priceMatch[1];
                                break;
                            }
                        }
                        
                        items.push({
                            itemCode,
                            description,
                            qty,
                            unit,
                            unitPrice,
                            amount
                        });
                    }
                    break;
            }
        }
    }
    
    return items;
}

module.exports = { extractFieldsFromText };